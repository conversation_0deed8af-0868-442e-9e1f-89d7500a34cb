using Castle.DynamicProxy;
using Core.Cache.Abstract;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Core.Aspects.Autofac.Caching
{
    /// <summary>
    /// Cache Aspect - Method sonuçlarını cache'ler
    /// Multi-tenant yapıya uygun, Redis tabanlı cache aspect
    /// </summary>
    public class CacheAspect : MethodInterception
    {
        private int _duration;
        private IRedisService _redisService;
        private ICacheKeyGenerator _keyGenerator;

        public CacheAspect(int duration = 60)
        {
            _duration = duration;
            _redisService = ServiceTool.ServiceProvider.GetService<IRedisService>();
            _keyGenerator = ServiceTool.ServiceProvider.GetService<ICacheKeyGenerator>();
        }

        public override void Intercept(IInvocation invocation)
        {
            // Redis health check - Circuit breaker pattern
            if (!IsRedisHealthy())
            {
                // Redis down - cache'i bypass et, direkt method'u çalıştır
                invocation.Proceed();
                return;
            }

            try
            {
                // Company ID'yi context'ten al
                var companyId = GetCompanyIdFromContext();

                // Cache key oluştur
                var methodName = string.Format($"{invocation.Method.ReflectedType.FullName}.{invocation.Method.Name}");
                var arguments = invocation.Arguments.ToList();

                // Method bazlı cache key
                var cacheKey = _keyGenerator.GenerateMethodKey(
                    companyId,
                    invocation.Method.ReflectedType.Name,
                    invocation.Method.Name,
                    invocation.Arguments);

                // Method'un return type'ını al
                var returnType = invocation.Method.ReturnType;

                // Cache'den string olarak al - 1000+ SALON OPTIMIZED
                var cacheTask = _redisService.GetAsync<string>(cacheKey);
                var timeoutTask = Task.Delay(2000); // 2 saniye timeout - 1000+ salon için optimize

                var completedTask = Task.WhenAny(cacheTask, timeoutTask).ConfigureAwait(false).GetAwaiter().GetResult();

                if (completedTask == cacheTask && cacheTask.IsCompletedSuccessfully && cacheTask.Result.Success && cacheTask.Result.IsFromCache)
                {
                    // Cache hit - JSON'dan deserialize et with error handling
                    var cachedJson = cacheTask.Result.Data;
                    if (!string.IsNullOrEmpty(cachedJson))
                    {
                        try
                        {
                            var deserializedValue = System.Text.Json.JsonSerializer.Deserialize(cachedJson, returnType);
                            invocation.ReturnValue = deserializedValue;
                            return;
                        }
                        catch (System.Text.Json.JsonException)
                        {
                            // JSON corruption - cache'i temizle ve method'u çalıştır
                            _ = Task.Run(async () => await _redisService.DeleteAsync(cacheKey));
                        }
                    }
                }
            }
            catch (Exception)
            {
                // Cache error - bypass cache
            }

            // Cache miss veya error - method'u çalıştır
            invocation.Proceed();

            // Sonucu cache'e kaydet - fire and forget
            if (invocation.ReturnValue != null)
            {
                _ = Task.Run(async () =>
                {
                    try
                    {
                        var companyId = GetCompanyIdFromContext();
                        var cacheKey = _keyGenerator.GenerateMethodKey(
                            companyId,
                            invocation.Method.ReflectedType?.Name ?? "Unknown",
                            invocation.Method.Name,
                            invocation.Arguments);

                        // JSON olarak serialize et ve string olarak kaydet
                        var json = System.Text.Json.JsonSerializer.Serialize(invocation.ReturnValue);
                        await _redisService.SetAsync(cacheKey, json, _duration);
                    }
                    catch
                    {
                        // Cache write error - ignore
                    }
                });
            }
        }

        private static DateTime _lastHealthCheck = DateTime.MinValue;
        private static bool _isRedisHealthy = true;
        private static readonly object _healthCheckLock = new();



        private static bool IsRedisHealthy()
        {
            lock (_healthCheckLock)
            {
                // Her 5 saniyede bir health check yap - enterprise responsiveness
                if (DateTime.Now.Subtract(_lastHealthCheck).TotalSeconds > 5)
                {
                    try
                    {
                        var connectionService = ServiceTool.ServiceProvider.GetService<Core.Cache.Abstract.IRedisConnectionService>();
                        _isRedisHealthy = connectionService?.IsConnected() ?? false;
                        _lastHealthCheck = DateTime.Now;
                    }
                    catch
                    {
                        _isRedisHealthy = false;
                        _lastHealthCheck = DateTime.Now;
                    }
                }

                return _isRedisHealthy;
            }
        }

        private static int GetCompanyIdFromContext()
        {
            try
            {
                // HttpContext'ten company ID al
                var httpContextAccessor = ServiceTool.ServiceProvider.GetService<Microsoft.AspNetCore.Http.IHttpContextAccessor>();
                var httpContext = httpContextAccessor?.HttpContext;

                if (httpContext?.User?.Identity?.IsAuthenticated == true)
                {
                    var companyIdClaim = httpContext.User.Claims.FirstOrDefault(c => c.Type == "CompanyId");
                    if (companyIdClaim != null && int.TryParse(companyIdClaim.Value, out var companyId))
                    {
                        return companyId;
                    }
                }

                // Fallback: Default company ID
                return 1;
            }
            catch
            {
                // Hata durumunda default company ID
                return 1;
            }
        }
    }
}
