using System.Reflection;

namespace Core.Cache.Abstract
{
    /// <summary>
    /// Multi-tenant cache key generation interface
    /// Company-based cache isolation için key generation servisi
    /// </summary>
    public interface ICacheKeyGenerator
    {
        /// <summary>
        /// Method bazlı cache key oluşturur
        /// </summary>
        /// <param name="companyId">Company ID (multi-tenant isolation)</param>
        /// <param name="serviceName">Service adı</param>
        /// <param name="methodName">Method adı</param>
        /// <param name="parameters">Method parametreleri</param>
        /// <returns>Unique cache key</returns>
        string GenerateMethodKey(int companyId, string serviceName, string methodName, object[] parameters);

        /// <summary>
        /// Entity bazlı cache key oluşturur
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="entityName">Entity adı</param>
        /// <param name="id">Entity ID</param>
        /// <returns>Entity cache key</returns>
        string GenerateEntityKey(int companyId, string entityName, int id);

        /// <summary>
        /// Entity list cache key oluşturur
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="entityName">Entity adı</param>
        /// <param name="filterHash">Filter parametrelerinin hash'i</param>
        /// <returns>List cache key</returns>
        string GenerateListKey(int companyId, string entityName, string filterHash);

        /// <summary>
        /// User session cache key oluşturur
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="userId">User ID</param>
        /// <param name="sessionType">Session tipi (login, refresh, etc.)</param>
        /// <returns>Session cache key</returns>
        string GenerateSessionKey(int companyId, int userId, string sessionType);

        /// <summary>
        /// Settings cache key oluşturur
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="settingKey">Setting anahtarı</param>
        /// <returns>Settings cache key</returns>
        string GenerateSettingsKey(int companyId, string settingKey);

        /// <summary>
        /// Rate limiting cache key oluşturur
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="identifier">Rate limit identifier (IP, UserID, etc.)</param>
        /// <param name="action">Action adı</param>
        /// <returns>Rate limit cache key</returns>
        string GenerateRateLimitKey(int companyId, string identifier, string action);

        /// <summary>
        /// Company bazlı pattern oluşturur (cache temizleme için)
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="pattern">Pattern (örn: "Member*", "Settings*")</param>
        /// <returns>Company-specific pattern</returns>
        string GenerateCompanyPattern(int companyId, string pattern);

        /// <summary>
        /// Method reflection bilgilerinden key oluşturur
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="methodInfo">Method reflection info</param>
        /// <param name="parameters">Method parametreleri</param>
        /// <returns>Method cache key</returns>
        string GenerateMethodKey(int companyId, MethodInfo methodInfo, object[] parameters);

        /// <summary>
        /// Parametrelerin hash'ini oluşturur
        /// </summary>
        /// <param name="parameters">Parametreler</param>
        /// <returns>Parameter hash</returns>
        string GenerateParameterHash(object[] parameters);
    }
}
