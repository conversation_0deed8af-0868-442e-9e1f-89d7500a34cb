using StackExchange.Redis;

namespace Core.Cache.Abstract
{
    /// <summary>
    /// Redis connection yönetimi için interface
    /// Multi-tenant yapıya uygun Redis bağlantı servisi
    /// </summary>
    public interface IRedisConnectionService
    {
        /// <summary>
        /// Redis database instance'ını döndürür
        /// </summary>
        /// <returns>IDatabase instance</returns>
        IDatabase GetDatabase();

        /// <summary>
        /// Redis server instance'ını döndürür
        /// </summary>
        /// <returns>IServer instance</returns>
        IServer GetServer();

        /// <summary>
        /// Redis bağlantı durumunu kontrol eder
        /// </summary>
        /// <returns>Bağlantı durumu</returns>
        bool IsConnected();

        /// <summary>
        /// Redis connection string'ini döndürür
        /// </summary>
        /// <returns>Connection string</returns>
        string GetConnectionString();

        /// <summary>
        /// Redis bağlantısını test eder
        /// </summary>
        /// <returns>Test sonucu</returns>
        Task<bool> TestConnectionAsync();

        /// <summary>
        /// Redis server bilgilerini döndürür
        /// </summary>
        /// <returns>Server info</returns>
        Task<string> GetServerInfoAsync();
    }
}
