using Core.Cache.Models;

namespace Core.Cache.Abstract
{
    /// <summary>
    /// Redis cache service interface
    /// Multi-tenant yapıya uygun, generic cache operations
    /// </summary>
    public interface IRedisService
    {
        #region Basic Operations

        /// <summary>
        /// Cache'den veri alır
        /// </summary>
        /// <typeparam name="T">Veri tipi</typeparam>
        /// <param name="key">Cache key</param>
        /// <returns>Cache operation result</returns>
        Task<CacheOperationResult<T>> GetAsync<T>(string key);

        /// <summary>
        /// Cache'e veri yazar
        /// </summary>
        /// <typeparam name="T">Veri tipi</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="value">Değer</param>
        /// <param name="ttlMinutes">TTL (dakika)</param>
        /// <returns>İşlem sonucu</returns>
        Task<bool> SetAsync<T>(string key, T value, int? ttlMinutes = null);

        /// <summary>
        /// Cache'den veri siler
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Silindi mi</returns>
        Task<bool> DeleteAsync(string key);

        /// <summary>
        /// Key'in varlığını kontrol eder
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Var mı</returns>
        Task<bool> ExistsAsync(string key);

        /// <summary>
        /// Key'in TTL'ini alır
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>TTL (saniye)</returns>
        Task<TimeSpan?> GetTTLAsync(string key);

        /// <summary>
        /// Key'in TTL'ini günceller
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <param name="ttlMinutes">Yeni TTL (dakika)</param>
        /// <returns>Güncellendi mi</returns>
        Task<bool> UpdateTTLAsync(string key, int ttlMinutes);

        #endregion

        #region Batch Operations

        /// <summary>
        /// Birden fazla key'i siler
        /// </summary>
        /// <param name="keys">Key listesi</param>
        /// <returns>Silinen key sayısı</returns>
        Task<long> DeleteManyAsync(IEnumerable<string> keys);

        /// <summary>
        /// Pattern'e uyan key'leri siler
        /// </summary>
        /// <param name="pattern">Pattern (wildcard destekli)</param>
        /// <returns>Silinen key sayısı</returns>
        Task<long> DeleteByPatternAsync(string pattern);

        /// <summary>
        /// Birden fazla key'in değerini alır
        /// </summary>
        /// <typeparam name="T">Veri tipi</typeparam>
        /// <param name="keys">Key listesi</param>
        /// <returns>Key-Value dictionary</returns>
        Task<Dictionary<string, T?>> GetManyAsync<T>(IEnumerable<string> keys);

        /// <summary>
        /// Birden fazla key-value çiftini yazar
        /// </summary>
        /// <typeparam name="T">Veri tipi</typeparam>
        /// <param name="keyValuePairs">Key-Value çiftleri</param>
        /// <param name="ttlMinutes">TTL (dakika)</param>
        /// <returns>İşlem sonucu</returns>
        Task<bool> SetManyAsync<T>(Dictionary<string, T> keyValuePairs, int? ttlMinutes = null);

        #endregion

        #region Multi-Tenant Operations

        /// <summary>
        /// Company bazlı cache temizleme
        /// </summary>
        /// <param name="options">Invalidation options</param>
        /// <returns>Silinen key sayısı</returns>
        Task<long> InvalidateCacheAsync(CacheInvalidationOptions options);

        /// <summary>
        /// Company'nin tüm cache'ini temizler
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <returns>Silinen key sayısı</returns>
        Task<long> ClearCompanyCacheAsync(int companyId);

        /// <summary>
        /// Company'nin belirli entity cache'lerini temizler
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="entityNames">Entity adları</param>
        /// <returns>Silinen key sayısı</returns>
        Task<long> ClearEntityCacheAsync(int companyId, params string[] entityNames);

        #endregion

        #region Statistics & Monitoring

        /// <summary>
        /// Cache istatistiklerini alır
        /// </summary>
        /// <returns>Cache statistics</returns>
        Task<CacheStatistics> GetStatisticsAsync();

        /// <summary>
        /// Cache istatistiklerini sıfırlar - 1000+ salon için optimize edildi
        /// </summary>
        /// <returns>İşlem sonucu</returns>
        Task<bool> ResetStatisticsAsync();

        /// <summary>
        /// Pattern'e uyan key'leri listeler
        /// </summary>
        /// <param name="pattern">Pattern</param>
        /// <param name="count">Maksimum key sayısı</param>
        /// <returns>Key listesi</returns>
        Task<List<string>> GetKeysByPatternAsync(string pattern, int count = 1000);

        /// <summary>
        /// Company'nin key'lerini listeler
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="count">Maksimum key sayısı</param>
        /// <returns>Key listesi</returns>
        Task<List<string>> GetCompanyKeysAsync(int companyId, int count = 1000);

        #endregion

        #region Health Check

        /// <summary>
        /// Redis bağlantı durumunu kontrol eder
        /// </summary>
        /// <returns>Sağlık durumu</returns>
        Task<bool> IsHealthyAsync();

        /// <summary>
        /// Redis server bilgilerini alır
        /// </summary>
        /// <returns>Server info</returns>
        Task<Dictionary<string, string>> GetServerInfoAsync();

        #endregion
    }
}
