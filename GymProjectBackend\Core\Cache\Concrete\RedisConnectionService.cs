using Core.Cache.Abstract;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;

namespace Core.Cache.Concrete
{
    /// <summary>
    /// Redis connection yönetimi için concrete service
    /// Multi-tenant yapıya uygun Redis bağlantı servisi
    /// </summary>
    public class RedisConnectionService : IRedisConnectionService, IDisposable
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<RedisConnectionService> _logger;
        private readonly Lazy<ConnectionMultiplexer> _connectionMultiplexer;
        private readonly string _environment;
        private readonly string _connectionString;

        public RedisConnectionService(IConfiguration configuration, ILogger<RedisConnectionService> logger)
        {
            _configuration = configuration;
            _logger = logger;
            
            // Environment'ı al
            _environment = _configuration["Environment"] ?? "dev";
            
            // Redis connection string'ini al
            _connectionString = _configuration[$"RedisConnectionStrings:{_environment}"];
            
            if (string.IsNullOrEmpty(_connectionString))
            {
                throw new InvalidOperationException($"'{_environment}' environment'ı için Redis connection string bulunamadı!");
            }

            // Lazy connection initialization
            _connectionMultiplexer = new Lazy<ConnectionMultiplexer>(() =>
            {
                try
                {
                    var configurationOptions = ConfigurationOptions.Parse(_connectionString);
                    
                    // 1000+ salon için enterprise optimization
                    configurationOptions.AbortOnConnectFail = false;
                    configurationOptions.ConnectRetry = 5; // Daha fazla retry
                    configurationOptions.ConnectTimeout = 5000; // 5 saniye - daha hızlı fail
                    configurationOptions.SyncTimeout = 5000; // 5 saniye
                    configurationOptions.AsyncTimeout = 5000; // 5 saniye
                    configurationOptions.KeepAlive = 30; // Daha sık keep-alive
                    configurationOptions.ReconnectRetryPolicy = new ExponentialRetry(500);

                    // 1000+ salon için security ve performance
                    configurationOptions.DefaultDatabase = 0;
                    configurationOptions.AllowAdmin = false; // Security için admin commands disable
                    configurationOptions.ChannelPrefix = "gymkod";
                    configurationOptions.ClientName = $"GymKod-{Environment.MachineName}";
                    configurationOptions.CommandMap = CommandMap.Create(new HashSet<string>
                    {
                        "FLUSHDB", "FLUSHALL" // Dangerous commands'ı disable et
                    }, available: false);
                    
                    var connection = ConnectionMultiplexer.Connect(configurationOptions);
                    
                    // Connection event handlers
                    connection.ConnectionFailed += OnConnectionFailed;
                    connection.ConnectionRestored += OnConnectionRestored;
                    connection.ErrorMessage += OnErrorMessage;
                    
                    _logger.LogInformation("Redis connection established successfully for environment: {Environment}", _environment);
                    
                    return connection;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Redis connection failed for environment: {Environment}", _environment);
                    throw;
                }
            });
        }

        public IDatabase GetDatabase()
        {
            try
            {
                return _connectionMultiplexer.Value.GetDatabase();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get Redis database");
                throw;
            }
        }

        public IServer GetServer()
        {
            try
            {
                var endpoints = _connectionMultiplexer.Value.GetEndPoints();
                return _connectionMultiplexer.Value.GetServer(endpoints.First());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get Redis server");
                throw;
            }
        }

        public bool IsConnected()
        {
            try
            {
                return _connectionMultiplexer.IsValueCreated && 
                       _connectionMultiplexer.Value.IsConnected;
            }
            catch
            {
                return false;
            }
        }

        public string GetConnectionString()
        {
            return _connectionString;
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                var database = GetDatabase();
                var testKey = $"test:connection:{Guid.NewGuid()}";
                var testValue = "connection_test";
                
                // Test write
                await database.StringSetAsync(testKey, testValue, TimeSpan.FromSeconds(10));
                
                // Test read
                var result = await database.StringGetAsync(testKey);
                
                // Cleanup
                await database.KeyDeleteAsync(testKey);
                
                var success = result == testValue;
                
                if (success)
                {
                    _logger.LogInformation("Redis connection test successful");
                }
                else
                {
                    _logger.LogWarning("Redis connection test failed - value mismatch");
                }
                
                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Redis connection test failed");
                return false;
            }
        }

        public async Task<string> GetServerInfoAsync()
        {
            try
            {
                var server = GetServer();
                var info = await server.InfoAsync();
                return info.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get Redis server info");
                return $"Error: {ex.Message}";
            }
        }

        private void OnConnectionFailed(object sender, ConnectionFailedEventArgs e)
        {
            _logger.LogError("Redis connection failed: {Exception}", e.Exception?.Message);
        }

        private void OnConnectionRestored(object sender, ConnectionFailedEventArgs e)
        {
            _logger.LogInformation("Redis connection restored");
        }

        private void OnErrorMessage(object sender, RedisErrorEventArgs e)
        {
            _logger.LogError("Redis error: {Message}", e.Message);
        }

        public void Dispose()
        {
            if (_connectionMultiplexer.IsValueCreated)
            {
                _connectionMultiplexer.Value?.Dispose();
            }
        }
    }
}
