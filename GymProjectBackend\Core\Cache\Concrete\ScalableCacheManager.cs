using Core.Cache.Abstract;
using Core.Cache.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Core.Cache.Concrete
{
    /// <summary>
    /// Scalable cache manager - 1000+ salon için optimize edilmiş cache yönetimi
    /// </summary>
    public class ScalableCacheManager
    {
        private readonly IRedisService _redisService;
        private readonly ICacheKeyGenerator _keyGenerator;
        private readonly ILogger<ScalableCacheManager> _logger;
        private readonly CachePartitioningStrategy _partitioningStrategy;
        private readonly CacheSettings _cacheSettings;

        public ScalableCacheManager(
            IRedisService redisService,
            ICacheKeyGenerator keyGenerator,
            ILogger<ScalableCacheManager> logger,
            IConfiguration configuration)
        {
            _redisService = redisService;
            _keyGenerator = keyGenerator;
            _logger = logger;
            _partitioningStrategy = new CachePartitioningStrategy();
            
            var environment = configuration["Environment"] ?? "dev";
            _cacheSettings = new CacheSettings();
            configuration.GetSection($"CacheSettings:{environment}").Bind(_cacheSettings);
        }

        /// <summary>
        /// 1000+ salon için optimize edilmiş cache get operation
        /// </summary>
        public async Task<CacheOperationResult<T>> GetAsync<T>(int companyId, string entityType, string key)
        {
            try
            {
                // Partition-aware key generation
                var partitionedKey = GeneratePartitionedKey(companyId, entityType, key);
                
                // Circuit breaker check
                if (!await _redisService.IsHealthyAsync())
                {
                    return new CacheOperationResult<T>
                    {
                        Success = false,
                        IsFromCache = false,
                        ErrorMessage = "Cache service unavailable"
                    };
                }

                return await _redisService.GetAsync<T>(partitionedKey);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in scalable cache get for company {CompanyId}, entity {EntityType}, key {Key}", 
                    companyId, entityType, key);
                
                return new CacheOperationResult<T>
                {
                    Success = false,
                    IsFromCache = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        /// <summary>
        /// 1000+ salon için optimize edilmiş cache set operation
        /// </summary>
        public async Task<bool> SetAsync<T>(int companyId, string entityType, string key, T value, int? ttlMinutes = null)
        {
            try
            {
                // Partition-aware key generation
                var partitionedKey = GeneratePartitionedKey(companyId, entityType, key);
                
                // TTL optimization based on entity type
                var optimizedTtl = GetOptimizedTtl(entityType, ttlMinutes);
                
                return await _redisService.SetAsync(partitionedKey, value, optimizedTtl);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in scalable cache set for company {CompanyId}, entity {EntityType}, key {Key}", 
                    companyId, entityType, key);
                return false;
            }
        }

        /// <summary>
        /// Batch operations - 1000+ salon için optimize edilmiş
        /// </summary>
        public async Task<Dictionary<string, T?>> GetManyAsync<T>(int companyId, string entityType, IEnumerable<string> keys)
        {
            try
            {
                var partitionedKeys = keys.Select(key => GeneratePartitionedKey(companyId, entityType, key)).ToList();
                
                // Batch size limit check
                if (partitionedKeys.Count > _cacheSettings.MaxBatchSize)
                {
                    _logger.LogWarning("Batch size {BatchSize} exceeds limit {MaxBatchSize} for company {CompanyId}", 
                        partitionedKeys.Count, _cacheSettings.MaxBatchSize, companyId);
                    
                    // Split into smaller batches
                    var result = new Dictionary<string, T?>();
                    var batches = partitionedKeys.Chunk(_cacheSettings.MaxBatchSize);
                    
                    foreach (var batch in batches)
                    {
                        var batchResult = await _redisService.GetManyAsync<T>(batch);
                        foreach (var item in batchResult)
                        {
                            result[item.Key] = item.Value;
                        }
                    }
                    
                    return result;
                }
                
                return await _redisService.GetManyAsync<T>(partitionedKeys);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in scalable cache get many for company {CompanyId}, entity {EntityType}", 
                    companyId, entityType);
                return new Dictionary<string, T?>();
            }
        }

        /// <summary>
        /// Company-wide cache invalidation - 1000+ salon için optimize edilmiş
        /// </summary>
        public async Task<long> InvalidateCompanyCacheAsync(int companyId, params string[] entityTypes)
        {
            try
            {
                var totalDeleted = 0L;
                
                foreach (var entityType in entityTypes)
                {
                    var pattern = GeneratePartitionedPattern(companyId, entityType);
                    var deleted = await _redisService.DeleteByPatternAsync(pattern);
                    totalDeleted += deleted;
                    
                    _logger.LogInformation("Invalidated {DeletedCount} cache entries for company {CompanyId}, entity {EntityType}", 
                        deleted, companyId, entityType);
                }
                
                return totalDeleted;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error invalidating cache for company {CompanyId}", companyId);
                return 0;
            }
        }

        /// <summary>
        /// Partition-aware key generation
        /// </summary>
        private string GeneratePartitionedKey(int companyId, string entityType, string key)
        {
            var partition = _partitioningStrategy.GetPartitionForDataType(entityType, companyId);
            return $"{_cacheSettings.KeyPrefix}:p{partition}:c{companyId}:{entityType}:{key}";
        }

        /// <summary>
        /// Partition-aware pattern generation
        /// </summary>
        private string GeneratePartitionedPattern(int companyId, string entityType)
        {
            var partition = _partitioningStrategy.GetPartitionForDataType(entityType, companyId);
            return $"{_cacheSettings.KeyPrefix}:p{partition}:c{companyId}:{entityType}:*";
        }

        /// <summary>
        /// Entity type'a göre optimize edilmiş TTL
        /// </summary>
        private int GetOptimizedTtl(string entityType, int? requestedTtl)
        {
            if (requestedTtl.HasValue)
                return requestedTtl.Value;

            // Entity type'a göre optimize edilmiş TTL values
            return entityType switch
            {
                "User" => 15, // Kullanıcı bilgileri - sık değişir
                "Member" => 30, // Üye bilgileri - orta sıklıkta değişir
                "MembershipType" => 120, // Üyelik türleri - az değişir
                "City" => 1440, // Şehirler - çok az değişir (24 saat)
                "Town" => 1440, // İlçeler - çok az değişir (24 saat)
                "Product" => 60, // Ürünler - orta sıklıkta değişir
                "Report" => 5, // Raporlar - sık değişir
                _ => _cacheSettings.DefaultTTLMinutes
            };
        }

        /// <summary>
        /// Cache health check - 1000+ salon için
        /// </summary>
        public async Task<Dictionary<string, object>> GetHealthStatusAsync()
        {
            try
            {
                var isHealthy = await _redisService.IsHealthyAsync();
                var statistics = await _redisService.GetStatisticsAsync();
                var serverInfo = await _redisService.GetServerInfoAsync();

                return new Dictionary<string, object>
                {
                    ["IsHealthy"] = isHealthy,
                    ["Statistics"] = statistics,
                    ["ServerInfo"] = serverInfo,
                    ["PartitioningStrategy"] = _partitioningStrategy,
                    ["CacheSettings"] = _cacheSettings
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cache health status");
                return new Dictionary<string, object>
                {
                    ["IsHealthy"] = false,
                    ["Error"] = ex.Message
                };
            }
        }
    }
}
