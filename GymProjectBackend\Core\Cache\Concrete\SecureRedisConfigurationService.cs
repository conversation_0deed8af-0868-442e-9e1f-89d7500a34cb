using Core.Cache.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;

namespace Core.Cache.Concrete
{
    /// <summary>
    /// Secure Redis configuration service - 1000+ salon için güvenli konfigürasyon
    /// </summary>
    public class SecureRedisConfigurationService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<SecureRedisConfigurationService> _logger;
        private readonly string _environment;

        public SecureRedisConfigurationService(
            IConfiguration configuration,
            ILogger<SecureRedisConfigurationService> logger)
        {
            _configuration = configuration;
            _logger = logger;
            _environment = _configuration["Environment"] ?? "dev";
        }

        /// <summary>
        /// Secure Redis configuration options oluştur
        /// </summary>
        public ConfigurationOptions CreateSecureConfiguration()
        {
            try
            {
                var securitySettings = GetSecuritySettings();
                var connectionString = GetSecureConnectionString();

                var options = ConfigurationOptions.Parse(connectionString);

                // 1000+ salon için security settings
                options.AbortOnConnectFail = false;
                options.ConnectRetry = 5;
                options.ConnectTimeout = 5000;
                options.SyncTimeout = 5000;
                options.AsyncTimeout = 5000;
                options.KeepAlive = 30;
                options.ReconnectRetryPolicy = new ExponentialRetry(500);

                // Security hardening
                options.AllowAdmin = !securitySettings.DisableAdminCommands;
                options.ChannelPrefix = "gymkod";
                options.ClientName = $"GymKod-{Environment.MachineName}-{_environment}";
                options.DefaultDatabase = 0;

                // SSL/TLS configuration
                if (securitySettings.UseSsl)
                {
                    options.Ssl = true;
                    options.SslHost = options.EndPoints[0].ToString();
                    
                    if (securitySettings.SkipCertificateValidation)
                    {
                        options.CertificateValidation += (sender, certificate, chain, errors) => true;
                    }
                }

                // Dangerous commands'ı disable et
                if (securitySettings.DisabledCommands?.Length > 0)
                {
                    options.CommandMap = CommandMap.Create(
                        new HashSet<string>(securitySettings.DisabledCommands), 
                        available: false);
                }

                _logger.LogInformation("Secure Redis configuration created for environment: {Environment}", _environment);
                return options;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating secure Redis configuration");
                throw;
            }
        }

        /// <summary>
        /// Environment variable'dan secure connection string al
        /// </summary>
        private string GetSecureConnectionString()
        {
            // Environment variable'dan al (production için)
            var envConnectionString = Environment.GetEnvironmentVariable($"REDIS_CONNECTION_{_environment.ToUpper()}");
            
            if (!string.IsNullOrEmpty(envConnectionString))
            {
                _logger.LogInformation("Using Redis connection string from environment variable");
                return envConnectionString;
            }

            // Fallback to appsettings (development için)
            var configConnectionString = _configuration[$"RedisConnectionStrings:{_environment}"];
            
            if (string.IsNullOrEmpty(configConnectionString))
            {
                throw new InvalidOperationException($"Redis connection string not found for environment: {_environment}");
            }

            _logger.LogWarning("Using Redis connection string from appsettings.json - consider using environment variables for production");
            return configConnectionString;
        }

        /// <summary>
        /// Security settings'i al
        /// </summary>
        private RedisSecuritySettings GetSecuritySettings()
        {
            var settings = new RedisSecuritySettings();
            _configuration.GetSection($"RedisSecurity:{_environment}").Bind(settings);

            // Production için default security settings
            if (_environment == "canlı")
            {
                settings.DisableAdminCommands = true;
                settings.EnableEncryption = true;
                settings.EnableRateLimit = true;
                settings.UseSsl = true;
            }

            return settings;
        }
    }
}
