namespace Core.Cache.Models
{
    /// <summary>
    /// Cache partitioning strategy - 1000+ salon için cache dağıtım stratejisi
    /// </summary>
    public class CachePartitioningStrategy
    {
        /// <summary>
        /// Partition count - Redis cluster için
        /// </summary>
        public int PartitionCount { get; set; } = 16;

        /// <summary>
        /// Company per partition limit
        /// </summary>
        public int CompaniesPerPartition { get; set; } = 100;

        /// <summary>
        /// Hot data partition - sık kullanılan data için
        /// </summary>
        public int HotDataPartition { get; set; } = 0;

        /// <summary>
        /// Cold data partition - az kullanılan data için
        /// </summary>
        public int ColdDataPartition { get; set; } = 15;

        /// <summary>
        /// Company ID'ye göre partition hesapla
        /// </summary>
        public int GetPartitionForCompany(int companyId)
        {
            // Consistent hashing ile partition belirle
            return Math.Abs(companyId.GetHashCode()) % PartitionCount;
        }

        /// <summary>
        /// Data type'a göre partition strategy
        /// </summary>
        public int GetPartitionForDataType(string dataType, int companyId)
        {
            // Hot data types - sık kullanılan veriler
            var hotDataTypes = new[]
            {
                "User", "Member", "MembershipType", "City", "Town"
            };

            // Cold data types - az kullanılan veriler
            var coldDataTypes = new[]
            {
                "Report", "Log", "Archive", "Backup"
            };

            if (hotDataTypes.Contains(dataType))
            {
                // Hot data için dedicated partition
                return HotDataPartition;
            }

            if (coldDataTypes.Contains(dataType))
            {
                // Cold data için dedicated partition
                return ColdDataPartition;
            }

            // Normal data için company-based partition
            return GetPartitionForCompany(companyId);
        }

        /// <summary>
        /// Load balancing için partition weights
        /// </summary>
        public Dictionary<int, double> GetPartitionWeights()
        {
            var weights = new Dictionary<int, double>();

            for (int i = 0; i < PartitionCount; i++)
            {
                if (i == HotDataPartition)
                {
                    weights[i] = 2.0; // Hot data partition'a daha fazla weight
                }
                else if (i == ColdDataPartition)
                {
                    weights[i] = 0.5; // Cold data partition'a daha az weight
                }
                else
                {
                    weights[i] = 1.0; // Normal weight
                }
            }

            return weights;
        }

        /// <summary>
        /// Memory allocation per partition (MB)
        /// </summary>
        public Dictionary<int, int> GetMemoryAllocationPerPartition(int totalMemoryMB)
        {
            var weights = GetPartitionWeights();
            var totalWeight = weights.Values.Sum();
            var allocation = new Dictionary<int, int>();

            foreach (var partition in weights)
            {
                var memoryForPartition = (int)(totalMemoryMB * (partition.Value / totalWeight));
                allocation[partition.Key] = Math.Max(memoryForPartition, 64); // Minimum 64MB per partition
            }

            return allocation;
        }
    }
}
