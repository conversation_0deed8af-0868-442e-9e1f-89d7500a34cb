namespace Core.Cache.Models
{
    /// <summary>
    /// Cache performance statistics - Enterprise monitoring için
    /// </summary>
    public class CacheStatistics
    {
        public long TotalHits { get; set; }
        public long TotalMisses { get; set; }
        public long TotalSets { get; set; }
        public long TotalDeletes { get; set; }
        public double AverageResponseTimeMs { get; set; }
        public DateTime LastResetDate { get; set; }
        
        // ENTERPRISE METRICS - 1000+ salon için
        public long TotalOperations => TotalHits + TotalMisses + TotalSets + TotalDeletes;
        public double HitRatio => TotalHits + TotalMisses > 0 
            ? (double)TotalHits / (TotalHits + TotalMisses) * 100 
            : 0;
        public double OperationsPerSecond => CalculateOpsPerSecond();
        public string PerformanceGrade => GetPerformanceGrade();
        
        private double CalculateOpsPerSecond()
        {
            var uptime = DateTime.Now - LastResetDate;
            return uptime.TotalSeconds > 0 ? TotalOperations / uptime.TotalSeconds : 0;
        }
        
        private string GetPerformanceGrade()
        {
            return HitRatio switch
            {
                >= 90 => "A+",
                >= 80 => "A", 
                >= 70 => "B",
                >= 60 => "C",
                >= 50 => "D",
                _ => "F"
            };
        }
    }
}
