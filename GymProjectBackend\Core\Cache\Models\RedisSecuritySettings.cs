using System.ComponentModel.DataAnnotations;

namespace Core.Cache.Models
{
    /// <summary>
    /// Redis security settings - 1000+ salon için güvenlik konfigürasyonu
    /// </summary>
    public class RedisSecuritySettings
    {
        /// <summary>
        /// Redis connection string - environment variable'dan al<PERSON>
        /// </summary>
        [Required]
        public string ConnectionString { get; set; } = string.Empty;

        /// <summary>
        /// Redis password - environment variable'dan alınacak
        /// </summary>
        public string? Password { get; set; }

        /// <summary>
        /// SSL/TLS kullanılacak mı
        /// </summary>
        public bool UseSsl { get; set; } = false;

        /// <summary>
        /// Certificate validation skip edilecek mi (dev için)
        /// </summary>
        public bool SkipCertificateValidation { get; set; } = false;

        /// <summary>
        /// Connection encryption aktif mi
        /// </summary>
        public bool EnableEncryption { get; set; } = true;

        /// <summary>
        /// Admin commands disabled mi
        /// </summary>
        public bool DisableAdminCommands { get; set; } = true;

        /// <summary>
        /// Dangerous commands listesi
        /// </summary>
        public string[] DisabledCommands { get; set; } = new[]
        {
            "FLUSHDB", "FLUSHALL", "SHUTDOWN", "DEBUG", "CONFIG", "EVAL", "SCRIPT"
        };

        /// <summary>
        /// Rate limiting aktif mi
        /// </summary>
        public bool EnableRateLimit { get; set; } = true;

        /// <summary>
        /// Saniyede maksimum operation sayısı
        /// </summary>
        public int MaxOperationsPerSecond { get; set; } = 1000;

        /// <summary>
        /// IP whitelist aktif mi
        /// </summary>
        public bool EnableIpWhitelist { get; set; } = false;

        /// <summary>
        /// Allowed IP addresses
        /// </summary>
        public string[] AllowedIpAddresses { get; set; } = Array.Empty<string>();
    }
}
