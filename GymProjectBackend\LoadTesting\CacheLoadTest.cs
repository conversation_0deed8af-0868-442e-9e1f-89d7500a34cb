using System.Diagnostics;
using System.Text.Json;
using StackExchange.Redis;

namespace LoadTesting
{
    /// <summary>
    /// 1000+ salon için Redis cache load testing
    /// ENTERPRISE SCALE TESTING - Ücretsiz!
    /// </summary>
    public class CacheLoadTest
    {
        private readonly IConnectionMultiplexer _redis;
        private readonly IDatabase _database;
        private readonly Random _random = new();

        public CacheLoadTest(string connectionString)
        {
            _redis = ConnectionMultiplexer.Connect(connectionString);
            _database = _redis.GetDatabase();
        }

        /// <summary>
        /// 1000 salon, 100K üye simülasyonu
        /// </summary>
        public async Task RunEnterpriseLoadTest()
        {
            Console.WriteLine("🚀 ENTERPRISE LOAD TEST BAŞLATIYOR...");
            Console.WriteLine("📊 Hedef: 1000 salon, 100K üye, 1M cache operation");
            
            var stopwatch = Stopwatch.StartNew();
            var tasks = new List<Task>();

            // 1000 salon simülasyonu
            for (int salonId = 1; salonId <= 1000; salonId++)
            {
                tasks.Add(SimulateSalonOperations(salonId));
                
                // Her 100 salon'da progress göster
                if (salonId % 100 == 0)
                {
                    Console.WriteLine($"✅ {salonId} salon simülasyonu başlatıldı...");
                }
            }

            await Task.WhenAll(tasks);
            stopwatch.Stop();

            Console.WriteLine($"🎯 LOAD TEST TAMAMLANDI!");
            Console.WriteLine($"⏱️  Toplam süre: {stopwatch.Elapsed.TotalSeconds:F2} saniye");
            Console.WriteLine($"🔥 Saniyede işlem: {1000000 / stopwatch.Elapsed.TotalSeconds:F0} ops/sec");
            
            await PrintPerformanceReport();
        }

        /// <summary>
        /// Tek salon için realistic operations
        /// </summary>
        private async Task SimulateSalonOperations(int salonId)
        {
            var tasks = new List<Task>();

            // Her salon için 100 üye simülasyonu
            for (int memberId = 1; memberId <= 100; memberId++)
            {
                var globalMemberId = (salonId - 1) * 100 + memberId;
                tasks.Add(SimulateMemberOperations(salonId, globalMemberId));
            }

            // Salon-level cache operations
            tasks.Add(SimulateSalonLevelOperations(salonId));

            await Task.WhenAll(tasks);
        }

        /// <summary>
        /// Üye bazlı cache operations
        /// </summary>
        private async Task SimulateMemberOperations(int salonId, int memberId)
        {
            var keyPrefix = $"gymkod:prod:company:{salonId}";

            // Member detail cache
            var memberKey = $"{keyPrefix}:member:{memberId}";
            var memberData = new
            {
                Id = memberId,
                Name = $"Member_{memberId}",
                Email = $"member{memberId}@salon{salonId}.com",
                SalonId = salonId,
                CreatedAt = DateTime.Now
            };

            await _database.StringSetAsync(memberKey, JsonSerializer.Serialize(memberData), TimeSpan.FromMinutes(30));

            // Membership cache
            var membershipKey = $"{keyPrefix}:membership:{memberId}";
            var membershipData = new
            {
                MemberId = memberId,
                PackageType = _random.Next(1, 5),
                StartDate = DateTime.Now.AddDays(-30),
                EndDate = DateTime.Now.AddDays(30),
                IsActive = true
            };

            await _database.StringSetAsync(membershipKey, JsonSerializer.Serialize(membershipData), TimeSpan.FromMinutes(60));

            // Random read operations (cache hits)
            for (int i = 0; i < 5; i++)
            {
                await _database.StringGetAsync(memberKey);
                await _database.StringGetAsync(membershipKey);
            }
        }

        /// <summary>
        /// Salon seviyesi operations
        /// </summary>
        private async Task SimulateSalonLevelOperations(int salonId)
        {
            var keyPrefix = $"gymkod:prod:company:{salonId}";

            // Dashboard data cache
            var dashboardKey = $"{keyPrefix}:dashboard:stats";
            var dashboardData = new
            {
                TotalMembers = 100,
                ActiveMembers = 85,
                MonthlyRevenue = _random.Next(50000, 200000),
                SalonId = salonId,
                LastUpdated = DateTime.Now
            };

            await _database.StringSetAsync(dashboardKey, JsonSerializer.Serialize(dashboardData), TimeSpan.FromMinutes(15));

            // Product list cache
            var productsKey = $"{keyPrefix}:products:all";
            var products = Enumerable.Range(1, 20).Select(i => new
            {
                Id = i,
                Name = $"Product_{i}",
                Price = _random.Next(10, 500),
                SalonId = salonId
            }).ToArray();

            await _database.StringSetAsync(productsKey, JsonSerializer.Serialize(products), TimeSpan.FromMinutes(45));

            // Expense reports cache
            var expensesKey = $"{keyPrefix}:expenses:monthly";
            var expenses = Enumerable.Range(1, 10).Select(i => new
            {
                Id = i,
                Amount = _random.Next(1000, 10000),
                Category = $"Category_{i % 5}",
                Date = DateTime.Now.AddDays(-i),
                SalonId = salonId
            }).ToArray();

            await _database.StringSetAsync(expensesKey, JsonSerializer.Serialize(expenses), TimeSpan.FromMinutes(30));

            // Frequent reads (realistic usage pattern)
            for (int i = 0; i < 10; i++)
            {
                await _database.StringGetAsync(dashboardKey);
                await _database.StringGetAsync(productsKey);
                
                if (i % 3 == 0) // Less frequent expense reads
                    await _database.StringGetAsync(expensesKey);
            }
        }

        /// <summary>
        /// Performance raporu
        /// </summary>
        private async Task PrintPerformanceReport()
        {
            Console.WriteLine("\n📈 PERFORMANCE RAPORU:");
            Console.WriteLine("=" * 50);

            try
            {
                // Redis INFO komutu
                var server = _redis.GetServer(_redis.GetEndPoints().First());
                var info = await server.InfoAsync();

                foreach (var section in info)
                {
                    if (section.Key == "memory" || section.Key == "stats" || section.Key == "clients")
                    {
                        Console.WriteLine($"\n[{section.Key.ToUpper()}]");
                        foreach (var item in section.Take(5)) // İlk 5 metric
                        {
                            Console.WriteLine($"  {item.Key}: {item.Value}");
                        }
                    }
                }

                // Custom metrics
                var keyCount = await server.DatabaseSizeAsync();
                Console.WriteLine($"\n[CUSTOM METRICS]");
                Console.WriteLine($"  Total Keys: {keyCount:N0}");
                Console.WriteLine($"  Estimated Memory per Key: {GetEstimatedMemoryPerKey()} bytes");
                Console.WriteLine($"  Projected 1000 Salon Memory: {GetProjectedMemoryUsage():F2} MB");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Performance raporu alınamadı: {ex.Message}");
            }
        }

        private int GetEstimatedMemoryPerKey()
        {
            // Ortalama key size estimation
            return 1024; // 1KB per key (conservative estimate)
        }

        private double GetProjectedMemoryUsage()
        {
            // 1000 salon * 100 üye * 3 key per member + salon level keys
            var totalKeys = 1000 * 100 * 3 + 1000 * 5;
            return (totalKeys * GetEstimatedMemoryPerKey()) / 1024.0 / 1024.0; // MB
        }

        public void Dispose()
        {
            _redis?.Dispose();
        }
    }

    /// <summary>
    /// Load test runner
    /// </summary>
    public class Program
    {
        public static async Task Main(string[] args)
        {
            var connectionString = args.Length > 0 
                ? args[0] 
                : "localhost:6379";

            Console.WriteLine($"🔗 Redis bağlantısı: {connectionString}");

            using var loadTest = new CacheLoadTest(connectionString);
            await loadTest.RunEnterpriseLoadTest();

            Console.WriteLine("\n✅ Load test tamamlandı. Herhangi bir tuşa basın...");
            Console.ReadKey();
        }
    }
}
