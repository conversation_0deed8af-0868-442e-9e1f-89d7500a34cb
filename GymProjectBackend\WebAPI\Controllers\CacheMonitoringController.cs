using Core.Cache.Abstract;
using Core.Cache.Models;
using Core.Utilities.Results;
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics;

namespace WebAPI.Controllers
{
    /// <summary>
    /// Cache monitoring ve performance dashboard
    /// 1000+ salon için enterprise-level monitoring - REAL-TIME DASHBOARD
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class CacheMonitoringController : ControllerBase
    {
        private readonly IRedisService _redisService;
        private readonly IRedisConnectionService _connectionService;
        private readonly IConfiguration _configuration;

        public CacheMonitoringController(
            IRedisService redisService,
            IRedisConnectionService connectionService,
            IConfiguration configuration)
        {
            _redisService = redisService;
            _connectionService = connectionService;
            _configuration = configuration;
        }

        /// <summary>
        /// Real-time cache dashboard data - 1000+ SALON READY
        /// </summary>
        [HttpGet("dashboard")]
        public async Task<IActionResult> GetDashboard()
        {
            try
            {
                var stats = await _redisService.GetStatisticsAsync();
                var isHealthy = await _redisService.IsHealthyAsync();
                var serverInfo = await _redisService.GetServerInfoAsync();

                // Memory info
                var process = Process.GetCurrentProcess();
                var serverMemory = GC.GetTotalMemory(false) / 1024 / 1024; // MB
                var environment = _configuration["Environment"] ?? "dev";

                var dashboard = new
                {
                    // Cache Performance - 1000+ salon metrics
                    CacheStats = new
                    {
                        TotalHits = stats.TotalHits,
                        TotalMisses = stats.TotalMisses,
                        TotalSets = stats.TotalSets,
                        TotalDeletes = stats.TotalDeletes,
                        HitRatio = stats.TotalHits + stats.TotalMisses > 0
                            ? Math.Round((double)stats.TotalHits / (stats.TotalHits + stats.TotalMisses) * 100, 2)
                            : 0,
                        TotalOperations = stats.TotalHits + stats.TotalMisses + stats.TotalSets + stats.TotalDeletes,
                        AverageResponseTime = Math.Round(stats.AverageResponseTimeMs, 2),
                        OperationsPerSecond = Math.Round(CalculateOpsPerSecond(stats), 2)
                    },

                    // System Health - 1000+ salon monitoring
                    Health = new
                    {
                        Status = isHealthy ? "HEALTHY" : "UNHEALTHY",
                        RedisConnected = isHealthy,
                        Environment = environment,
                        ServerMemoryMB = serverMemory,
                        ProcessMemoryMB = process.WorkingSet64 / 1024 / 1024,
                        Uptime = DateTime.Now - Process.GetCurrentProcess().StartTime,
                        LastResetDate = stats.LastResetDate,
                        RedisVersion = serverInfo.GetValueOrDefault("redis_version", "Unknown"),
                        ConnectedClients = serverInfo.GetValueOrDefault("connected_clients", "0")
                    },

                    // Performance Indicators - 1000+ salon grades
                    Performance = new
                    {
                        OperationsPerSecond = Math.Round(CalculateOpsPerSecond(stats), 2),
                        MemoryEfficiency = Math.Round(CalculateMemoryEfficiency(stats, serverMemory), 2),
                        ResponseTimeGrade = GetResponseTimeGrade(stats.AverageResponseTimeMs),
                        HitRatioGrade = GetHitRatioGrade(stats),
                        OverallGrade = GetOverallGrade(stats, isHealthy),
                        ScalabilityScore = CalculateScalabilityScore(stats, isHealthy)
                    },

                    // 1000+ salon readiness indicators
                    ScalabilityMetrics = new
                    {
                        MaxSupportedSalons = GetMaxSupportedSalons(stats),
                        CurrentLoad = GetCurrentLoadPercentage(stats),
                        RecommendedAction = GetScalabilityRecommendation(stats),
                        NextBottleneck = GetNextBottleneck(stats)
                    },

                    Timestamp = DateTime.Now
                };

                return Ok(new SuccessDataResult<object>(dashboard, "1000+ salon ready cache dashboard"));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorDataResult<object>(null, $"Dashboard error: {ex.Message}"));
            }
        }

        /// <summary>
        /// Cache performance trends (son 24 saat)
        /// </summary>
        [HttpGet("trends")]
        public async Task<IActionResult> GetTrends()
        {
            try
            {
                // Bu basit implementasyon - production'da time-series DB kullanılabilir
                var stats = await _redisService.GetStatisticsAsync();
                
                var trends = new
                {
                    HourlyStats = GenerateHourlyMockData(stats),
                    Recommendations = GenerateRecommendations(stats),
                    Alerts = GenerateAlerts(stats)
                };

                return Ok(new SuccessDataResult<object>(trends, "Cache trends data"));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorDataResult<object>(null, $"Trends error: {ex.Message}"));
            }
        }

        /// <summary>
        /// Cache health check endpoint
        /// </summary>
        [HttpGet("health")]
        public async Task<IActionResult> HealthCheck()
        {
            try
            {
                var isHealthy = await _connectionService.TestConnectionAsync();
                var stats = await _redisService.GetStatisticsAsync();

                var health = new
                {
                    Status = isHealthy ? "Healthy" : "Unhealthy",
                    RedisConnected = isHealthy,
                    TotalOperations = stats.TotalHits + stats.TotalMisses + stats.TotalSets,
                    HitRatio = stats.TotalHits + stats.TotalMisses > 0 
                        ? (double)stats.TotalHits / (stats.TotalHits + stats.TotalMisses) * 100 
                        : 0,
                    AverageResponseTime = stats.AverageResponseTimeMs,
                    CheckTime = DateTime.Now
                };

                return isHealthy 
                    ? Ok(new SuccessDataResult<object>(health, "Cache is healthy"))
                    : StatusCode(503, new ErrorDataResult<object>(health, "Cache is unhealthy"));
            }
            catch (Exception ex)
            {
                return StatusCode(503, new ErrorDataResult<object>(null, $"Health check failed: {ex.Message}"));
            }
        }

        /// <summary>
        /// Cache statistics reset
        /// </summary>
        [HttpPost("reset-stats")]
        public async Task<IActionResult> ResetStatistics()
        {
            try
            {
                await _redisService.ResetStatisticsAsync();
                return Ok(new SuccessResult("Cache statistics reset"));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorResult($"Reset failed: {ex.Message}"));
            }
        }

        #region Helper Methods

        private double CalculateOpsPerSecond(CacheStatistics stats)
        {
            var uptime = DateTime.Now - stats.LastResetDate;
            var totalOps = stats.TotalHits + stats.TotalMisses + stats.TotalSets + stats.TotalDeletes;
            
            return uptime.TotalSeconds > 0 ? totalOps / uptime.TotalSeconds : 0;
        }

        private double CalculateMemoryEfficiency(CacheStatistics stats, long serverMemoryMB)
        {
            // Basit efficiency calculation
            var hitRatio = stats.TotalHits + stats.TotalMisses > 0 
                ? (double)stats.TotalHits / (stats.TotalHits + stats.TotalMisses) 
                : 0;
            
            return hitRatio * 100; // Hit ratio as efficiency
        }

        private string GetResponseTimeGrade(double avgResponseTime)
        {
            return avgResponseTime switch
            {
                < 1 => "A+",
                < 5 => "A",
                < 10 => "B",
                < 50 => "C",
                < 100 => "D",
                _ => "F"
            };
        }

        private string GetHitRatioGrade(CacheStatistics stats)
        {
            var hitRatio = stats.TotalHits + stats.TotalMisses > 0
                ? (double)stats.TotalHits / (stats.TotalHits + stats.TotalMisses) * 100
                : 0;

            return hitRatio switch
            {
                >= 95 => "A+",
                >= 85 => "A",
                >= 75 => "B",
                >= 65 => "C",
                >= 50 => "D",
                _ => "F"
            };
        }

        private string GetOverallGrade(CacheStatistics stats, bool isHealthy)
        {
            if (!isHealthy) return "F";

            var hitRatio = stats.TotalHits + stats.TotalMisses > 0
                ? (double)stats.TotalHits / (stats.TotalHits + stats.TotalMisses) * 100
                : 0;

            var responseTime = stats.AverageResponseTimeMs;

            // Combined scoring: hit ratio (70%) + response time (30%)
            var score = (hitRatio * 0.7) + ((responseTime < 5 ? 100 : Math.Max(0, 100 - responseTime * 2)) * 0.3);

            return score switch
            {
                >= 90 => "A+",
                >= 80 => "A",
                >= 70 => "B",
                >= 60 => "C",
                >= 50 => "D",
                _ => "F"
            };
        }

        private int CalculateScalabilityScore(CacheStatistics stats, bool isHealthy)
        {
            if (!isHealthy) return 0;

            var hitRatio = stats.TotalHits + stats.TotalMisses > 0
                ? (double)stats.TotalHits / (stats.TotalHits + stats.TotalMisses) * 100
                : 0;

            var responseTime = stats.AverageResponseTimeMs;
            var opsPerSecond = CalculateOpsPerSecond(stats);

            // Scalability score calculation
            var hitRatioScore = Math.Min(100, hitRatio);
            var responseTimeScore = responseTime < 2 ? 100 : Math.Max(0, 100 - responseTime * 10);
            var throughputScore = Math.Min(100, opsPerSecond / 10); // 1000 ops/sec = 100 score

            return (int)((hitRatioScore + responseTimeScore + throughputScore) / 3);
        }

        private int GetMaxSupportedSalons(CacheStatistics stats)
        {
            var opsPerSecond = CalculateOpsPerSecond(stats);
            var responseTime = stats.AverageResponseTimeMs;

            // Rough calculation: 1 salon = ~10 ops/sec average
            // With current performance, how many salons can be supported?
            if (responseTime > 10) return Math.Min(50, (int)(opsPerSecond / 20)); // Conservative
            if (responseTime > 5) return Math.Min(500, (int)(opsPerSecond / 15));
            return Math.Min(1000, (int)(opsPerSecond / 10)); // Optimal
        }

        private double GetCurrentLoadPercentage(CacheStatistics stats)
        {
            var opsPerSecond = CalculateOpsPerSecond(stats);
            var maxCapacity = 1000.0; // Assume 1000 ops/sec max capacity

            return Math.Min(100, (opsPerSecond / maxCapacity) * 100);
        }

        private string GetScalabilityRecommendation(CacheStatistics stats)
        {
            var hitRatio = stats.TotalHits + stats.TotalMisses > 0
                ? (double)stats.TotalHits / (stats.TotalHits + stats.TotalMisses) * 100
                : 0;

            var responseTime = stats.AverageResponseTimeMs;
            var loadPercentage = GetCurrentLoadPercentage(stats);

            if (loadPercentage > 80) return "URGENT: Scale up server resources immediately";
            if (responseTime > 10) return "Optimize Redis configuration and connection pool";
            if (hitRatio < 70) return "Review and optimize TTL strategies";
            if (loadPercentage > 60) return "Plan for horizontal scaling";
            return "System performing optimally for current load";
        }

        private string GetNextBottleneck(CacheStatistics stats)
        {
            var responseTime = stats.AverageResponseTimeMs;
            var hitRatio = stats.TotalHits + stats.TotalMisses > 0
                ? (double)stats.TotalHits / (stats.TotalHits + stats.TotalMisses) * 100
                : 0;

            if (responseTime > 5) return "Network/Connection Pool";
            if (hitRatio < 80) return "Cache Strategy/TTL";
            return "Memory/CPU Resources";
        }

        private object[] GenerateHourlyMockData(CacheStatistics stats)
        {
            // Mock data - production'da gerçek time-series data kullanılır
            var data = new List<object>();
            var now = DateTime.Now;
            
            for (int i = 23; i >= 0; i--)
            {
                var hour = now.AddHours(-i);
                data.Add(new
                {
                    Hour = hour.ToString("HH:mm"),
                    Hits = Random.Shared.Next(100, 1000),
                    Misses = Random.Shared.Next(10, 100),
                    ResponseTime = Random.Shared.NextDouble() * 10
                });
            }
            
            return data.ToArray();
        }

        private string[] GenerateRecommendations(CacheStatistics stats)
        {
            var recommendations = new List<string>();
            
            var hitRatio = stats.TotalHits + stats.TotalMisses > 0 
                ? (double)stats.TotalHits / (stats.TotalHits + stats.TotalMisses) * 100
                : 0;
            
            if (hitRatio < 70)
                recommendations.Add("Cache hit ratio düşük. TTL değerlerini artırın.");
            
            if (stats.AverageResponseTimeMs > 10)
                recommendations.Add("Response time yüksek. Redis connection pool'u optimize edin.");
            
            if (recommendations.Count == 0)
                recommendations.Add("Cache performansı optimal durumda!");
            
            return recommendations.ToArray();
        }

        private string[] GenerateAlerts(CacheStatistics stats)
        {
            var alerts = new List<string>();
            
            if (stats.AverageResponseTimeMs > 50)
                alerts.Add("CRITICAL: Response time çok yüksek!");
            
            var hitRatio = stats.TotalHits + stats.TotalMisses > 0 
                ? (double)stats.TotalHits / (stats.TotalMisses + stats.TotalMisses) * 100
                : 0;
            
            if (hitRatio < 50)
                alerts.Add("WARNING: Cache hit ratio çok düşük!");
            
            return alerts.ToArray();
        }

        #endregion
    }
}
