using Core.Cache.Abstract;
using Core.Cache.Models;
using Core.Utilities.Results;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    /// <summary>
    /// Redis cache test controller
    /// Geliştirme aşamasında Redis bağlantısını test etmek için
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class CacheTestController : ControllerBase
    {
        private readonly IRedisConnectionService _redisConnectionService;
        private readonly IRedisService _redisService;
        private readonly ICacheKeyGenerator _keyGenerator;

        public CacheTestController(
            IRedisConnectionService redisConnectionService,
            IRedisService redisService,
            ICacheKeyGenerator keyGenerator)
        {
            _redisConnectionService = redisConnectionService;
            _redisService = redisService;
            _keyGenerator = keyGenerator;
        }

        /// <summary>
        /// Redis bağlantı durumunu kontrol eder
        /// </summary>
        /// <returns>Bağlantı durumu</returns>
        [HttpGet("connection-status")]
        public IActionResult GetConnectionStatus()
        {
            try
            {
                var isConnected = _redisConnectionService.IsConnected();
                var connectionString = _redisConnectionService.GetConnectionString();
                
                return Ok(new SuccessDataResult<object>(new
                {
                    IsConnected = isConnected,
                    ConnectionString = connectionString.Split(',')[0], // Sadece host:port göster
                    Timestamp = DateTime.Now
                }, "Redis bağlantı durumu alındı"));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorDataResult<object>(null, $"Redis bağlantı durumu alınamadı: {ex.Message}"));
            }
        }

        /// <summary>
        /// Redis bağlantısını test eder
        /// </summary>
        /// <returns>Test sonucu</returns>
        [HttpPost("test-connection")]
        public async Task<IActionResult> TestConnection()
        {
            try
            {
                var testResult = await _redisConnectionService.TestConnectionAsync();
                
                if (testResult)
                {
                    return Ok(new SuccessResult("Redis bağlantı testi başarılı"));
                }
                else
                {
                    return BadRequest(new ErrorResult("Redis bağlantı testi başarısız"));
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorResult($"Redis bağlantı testi sırasında hata: {ex.Message}"));
            }
        }

        /// <summary>
        /// Redis server bilgilerini döndürür
        /// </summary>
        /// <returns>Server bilgileri</returns>
        [HttpGet("server-info")]
        public async Task<IActionResult> GetServerInfo()
        {
            try
            {
                var serverInfo = await _redisConnectionService.GetServerInfoAsync();
                
                return Ok(new SuccessDataResult<object>(new
                {
                    ServerInfo = serverInfo,
                    Timestamp = DateTime.Now
                }, "Redis server bilgileri alındı"));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorDataResult<object>(null, $"Redis server bilgileri alınamadı: {ex.Message}"));
            }
        }

        /// <summary>
        /// Basit cache test işlemi
        /// </summary>
        /// <returns>Cache test sonucu</returns>
        [HttpPost("test-cache")]
        public async Task<IActionResult> TestCache()
        {
            try
            {
                var database = _redisConnectionService.GetDatabase();
                var testKey = $"test:cache:{Guid.NewGuid()}";
                var testValue = $"Test value - {DateTime.Now}";

                // Cache'e yaz
                await database.StringSetAsync(testKey, testValue, TimeSpan.FromMinutes(1));

                // Cache'den oku
                var cachedValue = await database.StringGetAsync(testKey);

                // Temizle
                await database.KeyDeleteAsync(testKey);

                return Ok(new SuccessDataResult<object>(new
                {
                    TestKey = testKey,
                    OriginalValue = testValue,
                    CachedValue = cachedValue.ToString(),
                    IsMatch = cachedValue == testValue,
                    Timestamp = DateTime.Now
                }, "Cache test tamamlandı"));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorDataResult<object>(null, $"Cache test sırasında hata: {ex.Message}"));
            }
        }

        /// <summary>
        /// Redis Service ile cache test
        /// </summary>
        /// <returns>Redis service test sonucu</returns>
        [HttpPost("test-redis-service")]
        public async Task<IActionResult> TestRedisService()
        {
            try
            {
                var companyId = 1; // Test company
                var testData = new
                {
                    Id = 123,
                    Name = "Test Member",
                    Email = "<EMAIL>",
                    CreatedAt = DateTime.Now
                };

                // Key generation test
                var entityKey = _keyGenerator.GenerateEntityKey(companyId, "Member", testData.Id);
                var methodKey = _keyGenerator.GenerateMethodKey(companyId, "MemberService", "GetById", new object[] { testData.Id });

                // Set test
                var setResult = await _redisService.SetAsync(entityKey, testData, 5); // 5 dakika TTL

                // Get test
                var getResult = await _redisService.GetAsync<object>(entityKey);

                // TTL test
                var ttl = await _redisService.GetTTLAsync(entityKey);

                // Exists test
                var exists = await _redisService.ExistsAsync(entityKey);

                // Delete test
                var deleteResult = await _redisService.DeleteAsync(entityKey);

                return Ok(new SuccessDataResult<object>(new
                {
                    EntityKey = entityKey,
                    MethodKey = methodKey,
                    SetResult = setResult,
                    GetResult = new
                    {
                        Success = getResult.Success,
                        IsFromCache = getResult.IsFromCache,
                        Data = getResult.Data,
                        TTLSeconds = getResult.TTLSeconds,
                        ExecutionTimeMs = getResult.ExecutionTimeMs
                    },
                    TTL = ttl?.TotalSeconds,
                    Exists = exists,
                    DeleteResult = deleteResult,
                    Timestamp = DateTime.Now
                }, "Redis service test tamamlandı"));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorDataResult<object>(null, $"Redis service test sırasında hata: {ex.Message}"));
            }
        }

        /// <summary>
        /// Multi-tenant cache test
        /// </summary>
        /// <returns>Multi-tenant test sonucu</returns>
        [HttpPost("test-multi-tenant")]
        public async Task<IActionResult> TestMultiTenant()
        {
            try
            {
                var company1Id = 1;
                var company2Id = 2;
                var testData = "Test data for multi-tenant";

                // Her company için ayrı key'ler oluştur
                var key1 = _keyGenerator.GenerateEntityKey(company1Id, "Member", 123);
                var key2 = _keyGenerator.GenerateEntityKey(company2Id, "Member", 123);

                // Her company için cache'e yaz
                await _redisService.SetAsync(key1, $"{testData} - Company {company1Id}", 5);
                await _redisService.SetAsync(key2, $"{testData} - Company {company2Id}", 5);

                // Her company'den oku
                var result1 = await _redisService.GetAsync<string>(key1);
                var result2 = await _redisService.GetAsync<string>(key2);

                // Company 1'in cache'ini temizle
                var deletedCount = await _redisService.ClearCompanyCacheAsync(company1Id);

                // Tekrar oku (company 1 miss, company 2 hit olmalı)
                var result1After = await _redisService.GetAsync<string>(key1);
                var result2After = await _redisService.GetAsync<string>(key2);

                // Company 2'yi de temizle
                await _redisService.ClearCompanyCacheAsync(company2Id);

                return Ok(new SuccessDataResult<object>(new
                {
                    Company1Key = key1,
                    Company2Key = key2,
                    BeforeClear = new
                    {
                        Company1 = new { Success = result1.Success, Data = result1.Data, IsFromCache = result1.IsFromCache },
                        Company2 = new { Success = result2.Success, Data = result2.Data, IsFromCache = result2.IsFromCache }
                    },
                    DeletedKeysCount = deletedCount,
                    AfterClear = new
                    {
                        Company1 = new { Success = result1After.Success, Data = result1After.Data, IsFromCache = result1After.IsFromCache },
                        Company2 = new { Success = result2After.Success, Data = result2After.Data, IsFromCache = result2After.IsFromCache }
                    },
                    Timestamp = DateTime.Now
                }, "Multi-tenant cache test tamamlandı"));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorDataResult<object>(null, $"Multi-tenant test sırasında hata: {ex.Message}"));
            }
        }

        /// <summary>
        /// Cache istatistiklerini döndürür
        /// </summary>
        /// <returns>Cache statistics</returns>
        [HttpGet("statistics")]
        public async Task<IActionResult> GetStatistics()
        {
            try
            {
                var stats = await _redisService.GetStatisticsAsync();

                return Ok(new SuccessDataResult<CacheStatistics>(stats, "Cache istatistikleri alındı"));
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorDataResult<CacheStatistics>(null, $"Cache istatistikleri alınamadı: {ex.Message}"));
            }
        }

        /// <summary>
        /// Cache istatistiklerini sıfırlar
        /// </summary>
        /// <returns>Reset sonucu</returns>
        [HttpPost("reset-statistics")]
        public async Task<IActionResult> ResetStatistics()
        {
            try
            {
                var result = await _redisService.ResetStatisticsAsync();

                if (result)
                {
                    return Ok(new SuccessResult("Cache istatistikleri sıfırlandı"));
                }
                else
                {
                    return BadRequest(new ErrorResult("Cache istatistikleri sıfırlanamadı"));
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new ErrorResult($"Cache istatistikleri sıfırlanırken hata: {ex.Message}"));
            }
        }
    }
}
