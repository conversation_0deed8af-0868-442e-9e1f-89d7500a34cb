===============================================================================
                    REDIS SERVER KURULUM REHBERİ - WINDOWS SERVER 2019
                           GYM PROJECT - PRODUCTION READY
===============================================================================

📋 GENEL BİLGİLER:
- Redis Version: 3.2.100 (Windows)
- Platform: Windows Server 2019
- Port: 6379
- Memory Limit: 1GB
- Security: Password Protected
- Service Type: Windows Service (Automatic)

🎯 KURULUM AMACI:
- .NET Core Web API projesi için cache sistemi
- Multi-tenant mimari desteği
- 1000+ spor salonu kapasitesi
- Production-ready güvenlik ve performance

===============================================================================
                                ADIM 1: SİSTEM HAZIRLIĞI
===============================================================================

1.1) Windows Server'da PowerShell'i ADMINISTRATOR olarak aç

1.2) Sistem kontrolü yap:
```powershell
# Sistem bilgilerini kontrol et
Get-ComputerInfo | Select-Object WindowsProductName, WindowsVersion, @{Name="RAM (GB)";Expression={[math]::Round($_.TotalPhysicalMemory/1GB,2)}}

# Mevcut Redis var mı kontrol et
Get-Service | Where-Object {$_.Name -like "*redis*"}

# Port 6379 boş mu kontrol et
netstat -an | findstr :6379
```

BEKLENEN SONUÇ:
- Windows Server 2019 görünmeli
- Redis service olmamalı
- Port 6379 boş olmalı

===============================================================================
                            ADIM 2: KLASÖR YAPISI OLUŞTURMA
===============================================================================

2.1) Gerekli klasörleri oluştur:
```powershell
# Redis klasörleri oluştur
New-Item -ItemType Directory -Force -Path "C:\Redis" | Out-Null
New-Item -ItemType Directory -Force -Path "C:\Temp" | Out-Null
New-Item -ItemType Directory -Force -Path "C:\Redis\data" | Out-Null
New-Item -ItemType Directory -Force -Path "C:\Redis\logs" | Out-Null
New-Item -ItemType Directory -Force -Path "C:\Redis\config" | Out-Null

# Oluşturulan klasörleri kontrol et
Get-ChildItem -Path "C:\Redis" -Directory | Format-Table Name, CreationTime
```

BEKLENEN SONUÇ:
- C:\Redis\config
- C:\Redis\data  
- C:\Redis\logs
klasörleri oluşmalı

===============================================================================
                            ADIM 3: REDIS BINARY İNDİRME
===============================================================================

3.1) Redis Windows binary'sini indir:
```powershell
# TLS ayarları
[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12

# Redis MSI dosyasını indir
$redisUrl = "https://github.com/microsoftarchive/redis/releases/download/win-3.2.100/Redis-x64-3.2.100.msi"
$downloadPath = "C:\Temp\Redis-x64-3.2.100.msi"

Invoke-WebRequest -Uri $redisUrl -OutFile $downloadPath -UseBasicParsing

# Dosya boyutunu kontrol et
$fileSize = (Get-Item $downloadPath).Length / 1MB
Write-Host "Dosya boyutu: $([math]::Round($fileSize,2)) MB"
```

BEKLENEN SONUÇ:
- Dosya boyutu: ~5.8 MB
- Dosya konumu: C:\Temp\Redis-x64-3.2.100.msi

===============================================================================
                                ADIM 4: REDIS KURULUMU
===============================================================================

4.1) MSI kurulumunu yap:
```powershell
# MSI sessiz kurulum
$msiPath = "C:\Temp\Redis-x64-3.2.100.msi"
$installPath = "C:\Redis"

$arguments = @(
    "/i"
    "`"$msiPath`""
    "/quiet"
    "/norestart"
    "INSTALLDIR=`"$installPath`""
    "ADDLOCAL=ALL"
)

$process = Start-Process -FilePath "msiexec.exe" -ArgumentList $arguments -Wait -PassThru
```

4.2) Kurulum dosyalarını kontrol et:
```powershell
# Redis dosyalarını ara
Get-ChildItem -Path "C:\" -Recurse -Name "redis-server.exe" -ErrorAction SilentlyContinue
```

BEKLENEN SONUÇ:
- Redis dosyaları C:\Program Files\Redis\ altında olmalı
- redis-server.exe, redis-cli.exe, redis.windows-service.conf bulunmalı

===============================================================================
                            ADIM 5: İLK BAŞLATMA VE TEST
===============================================================================

5.1) Redis servisini başlat:
```powershell
Start-Service -Name "Redis"
Get-Service -Name "Redis"
```

5.2) Bağlantı testi:
```powershell
$redisCliPath = "C:\Program Files\Redis\redis-cli.exe"
& $redisCliPath ping
```

BEKLENEN SONUÇ:
- Service Status: Running
- PING sonucu: PONG

===============================================================================
                            ADIM 6: GÜVENLİK AYARLARI
===============================================================================

6.1) Güçlü password oluştur:
```powershell
$redisPassword = "GymKod2024_Redis_Secure_" + (Get-Random -Minimum 1000 -Maximum 9999) + "_Prod"
Write-Host "Redis Password: $redisPassword"
```

⚠️ ÖNEMLİ: Bu şifreyi kaydet! .NET Core projende kullanacaksın.

6.2) Konfigürasyon backup al:
```powershell
$configPath = "C:\Program Files\Redis\redis.windows-service.conf"
$backupPath = "C:\Redis\config\redis.windows-service.conf.backup"
Copy-Item $configPath $backupPath -Force
```

6.3) Password ayarını ekle:
```powershell
$passwordConfig = @"

# GYM PROJECT SECURITY - Password Authentication
requirepass $redisPassword
"@

Add-Content -Path $configPath -Value $passwordConfig
```

6.4) Redis'i yeniden başlat:
```powershell
Restart-Service -Name "Redis" -Force
Start-Sleep -Seconds 5
```

6.5) Şifreli bağlantı testi:
```powershell
& $redisCliPath -a $redisPassword ping
```

BEKLENEN SONUÇ: PONG

===============================================================================
                        ADIM 7: PERFORMANCE OPTİMİZASYONU
===============================================================================

7.1) Performance ayarları ekle:
```powershell
$performanceConfig = @"

# GYM PROJECT - PERFORMANCE SETTINGS
maxmemory 1gb
maxmemory-policy allkeys-lru
"@

Add-Content -Path $configPath -Value $performanceConfig
```

7.2) Redis'i yeniden başlat:
```powershell
Restart-Service -Name "Redis" -Force
Start-Sleep -Seconds 5
```

===============================================================================
                            ADIM 8: FİNAL TEST VE DOĞRULAMA
===============================================================================

8.1) Kapsamlı test yap:
```powershell
$redisCliPath = "C:\Program Files\Redis\redis-cli.exe"
$redisPassword = "YUKARIDA_OLUŞTURDUĞUN_ŞİFRE"

# Bağlantı testi
& $redisCliPath -a $redisPassword ping

# Veri yazma/okuma testi
& $redisCliPath -a $redisPassword set "test:key" "test_value"
& $redisCliPath -a $redisPassword get "test:key"

# Multi-tenant test
& $redisCliPath -a $redisPassword set "company:1:member:123" "Salon1_Data"
& $redisCliPath -a $redisPassword set "company:2:member:123" "Salon2_Data"
& $redisCliPath -a $redisPassword get "company:1:member:123"
& $redisCliPath -a $redisPassword get "company:2:member:123"

# Memory bilgisi
& $redisCliPath -a $redisPassword info memory | Select-String "used_memory_human", "maxmemory_human"

# Test verilerini temizle
& $redisCliPath -a $redisPassword del "test:key"
& $redisCliPath -a $redisPassword del "company:1:member:123"
& $redisCliPath -a $redisPassword del "company:2:member:123"
```

BEKLENEN SONUÇLAR:
- PING: PONG
- Veri yazma/okuma: Başarılı
- Multi-tenant keys: Farklı company'ler için ayrı data
- Memory: ~1GB limit görünmeli

===============================================================================
                            SORUN GİDERME REHBERİ
===============================================================================

🚨 SORUN: Redis servisi başlamıyor
ÇÖZÜM:
```powershell
# Backup'tan temiz konfigürasyon geri yükle
Copy-Item "C:\Redis\config\redis.windows-service.conf.backup" "C:\Program Files\Redis\redis.windows-service.conf" -Force
Start-Service -Name "Redis"
```

🚨 SORUN: Bağlantı hatası
ÇÖZÜM:
```powershell
# Port kontrolü
netstat -an | findstr :6379
# Process kontrolü  
Get-Process -Name "redis-server" -ErrorAction SilentlyContinue
# Event log kontrolü
Get-EventLog -LogName System -Source "Service Control Manager" -Newest 5 | Where-Object {$_.Message -like "*Redis*"}
```

🚨 SORUN: Performance düşük
ÇÖZÜM:
- Memory limit'i artır (maxmemory 2gb)
- Memory policy kontrol et (allkeys-lru)
- Disk I/O kontrol et

===============================================================================
                        .NET CORE ENTEGRASYON BİLGİLERİ
===============================================================================

📋 REDIS CONNECTION BİLGİLERİ:
- Host: 127.0.0.1
- Port: 6379  
- Password: [YUKARIDA OLUŞTURDUĞUN ŞİFRE]
- Connection String: "127.0.0.1:6379,password=[ŞİFRE]"

📦 GEREKLİ NUGET PACKAGES:
- StackExchange.Redis
- Microsoft.Extensions.Caching.StackExchangeRedis

🔑 MULTI-TENANT KEY STRATEGY:
- User Data: company:{companyId}:user:{userId}
- Member Data: company:{companyId}:member:{memberId}
- Session Data: company:{companyId}:session:{sessionId}
- Settings: company:{companyId}:settings:{key}

===============================================================================
                            YAPAY ZEKA İÇİN BİLGİLER
===============================================================================

Bu rehber Windows Server 2019'da Redis 3.2.100 kurulumu içindir.

KURULUM ÖZETİ:
- Redis Windows binary (MSI) kurulumu yapıldı
- Password authentication eklendi
- 1GB memory limit ayarlandı
- allkeys-lru eviction policy kullanıldı
- Windows Service olarak çalışıyor
- Multi-tenant key structure hazır

KULLANIM AMACI:
- .NET Core Web API cache sistemi
- Spor salonu yönetim sistemi
- Multi-tenant mimari
- Production environment

SORUN YAŞARSAN:
1. Bu rehberdeki adımları sırayla takip et
2. Sorun giderme bölümünü kontrol et  
3. Yapay zekaya "Redis Windows kurulumu sorunu" diye sor
4. Event Log'ları kontrol et
5. Redis CLI ile manuel test yap

===============================================================================
                                    SON
===============================================================================

Bu rehberi takip ederek Redis'i yeni sunucuya kurabilirsin.
Herhangi bir adımda takılırsan, o adımı tekrar yap veya yapay zekaya sor.

Başarılar! 🚀
