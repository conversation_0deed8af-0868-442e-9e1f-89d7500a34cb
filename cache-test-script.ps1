# Redis Cache Test Script
$baseUrl = "http://localhost:5165/api"

Write-Host "CACHE SISTEMI TEST BASLADI" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# 1. Basit Cache Testi
Write-Host "`n1. Basit Cache Testi..." -ForegroundColor Yellow
try {
    $response1 = Invoke-RestMethod -Uri "$baseUrl/cachetest/test-cache" -Method POST -ContentType "application/json"
    Write-Host "Basit cache testi basarili!" -ForegroundColor Green
    Write-Host "   Test Key: $($response1.data.TestKey)" -ForegroundColor Cyan
    Write-Host "   Deger Eslesmesi: $($response1.data.IsMatch)" -ForegroundColor Cyan
} catch {
    Write-Host "Basit cache testi basarisiz: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. Redis Service Testi
Write-Host "`n2. Redis Service Testi..." -ForegroundColor Yellow
try {
    $response2 = Invoke-RestMethod -Uri "$baseUrl/cachetest/test-redis-service" -Method POST -ContentType "application/json"
    Write-Host "Redis service testi basarili!" -ForegroundColor Green
    Write-Host "   Entity Key: $($response2.data.EntityKey)" -ForegroundColor Cyan
    Write-Host "   Set Sonucu: $($response2.data.SetResult)" -ForegroundColor Cyan
    Write-Host "   Get Basarili: $($response2.data.GetResult.Success)" -ForegroundColor Cyan
    Write-Host "   Cache'den Geldi: $($response2.data.GetResult.IsFromCache)" -ForegroundColor Cyan
    Write-Host "   TTL: $($response2.data.TTL) saniye" -ForegroundColor Cyan
} catch {
    Write-Host "Redis service testi basarisiz: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. Cache Istatistikleri
Write-Host "`n3. Cache Istatistikleri..." -ForegroundColor Yellow
try {
    $response3 = Invoke-RestMethod -Uri "$baseUrl/cachetest/statistics" -Method GET
    Write-Host "Cache istatistikleri alindi!" -ForegroundColor Green
    Write-Host "   Toplam Hit: $($response3.data.TotalHits)" -ForegroundColor Cyan
    Write-Host "   Toplam Miss: $($response3.data.TotalMisses)" -ForegroundColor Cyan
    Write-Host "   Hit Orani: $($response3.data.HitRatio)%" -ForegroundColor Cyan
    Write-Host "   Toplam Set: $($response3.data.TotalSets)" -ForegroundColor Cyan
} catch {
    Write-Host "Cache istatistikleri alinamadi: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nCACHE TEST TAMAMLANDI!" -ForegroundColor Green

# 4. Multi-Tenant Testi
Write-Host "`n4. Multi-Tenant Testi..." -ForegroundColor Yellow
try {
    $response4 = Invoke-RestMethod -Uri "$baseUrl/cachetest/test-multi-tenant" -Method POST -ContentType "application/json"
    Write-Host "Multi-tenant testi basarili!" -ForegroundColor Green
    Write-Host "   Company 1 Key: $($response4.data.Company1Key)" -ForegroundColor Cyan
    Write-Host "   Company 2 Key: $($response4.data.Company2Key)" -ForegroundColor Cyan
    Write-Host "   Izolasyon Basarili: $($response4.data.IsolationSuccessful)" -ForegroundColor Cyan
} catch {
    Write-Host "Multi-tenant testi basarisiz: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. Performans Testi
Write-Host "`n5. Performans Testi (10 islem)..." -ForegroundColor Yellow
$stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
$successCount = 0

for ($i = 1; $i -le 10; $i++) {
    try {
        $null = Invoke-RestMethod -Uri "$baseUrl/cachetest/test-cache" -Method POST -ContentType "application/json"
        Write-Host "." -NoNewline -ForegroundColor Green
        $successCount++
    } catch {
        Write-Host "x" -NoNewline -ForegroundColor Red
    }
}

$stopwatch.Stop()
Write-Host "`n10 cache islemi $($stopwatch.ElapsedMilliseconds)ms'de tamamlandi" -ForegroundColor Green
Write-Host "   Ortalama: $([math]::Round($stopwatch.ElapsedMilliseconds / 10, 2))ms/islem" -ForegroundColor Cyan
Write-Host "   Basari Orani: $([math]::Round($successCount / 10 * 100, 1))%" -ForegroundColor Cyan

Write-Host "`n================================" -ForegroundColor Green
Write-Host "CACHE SISTEMI TAMAMEN CALISIR DURUMDA!" -ForegroundColor Green
